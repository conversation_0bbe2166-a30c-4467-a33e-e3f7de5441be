<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1759215238954" clover="3.2.0">
  <project timestamp="1759215238954" name="All files">
    <metrics statements="4" coveredstatements="4" conditionals="2" coveredconditionals="1" methods="0" coveredmethods="0" elements="6" coveredelements="5" complexity="0" loc="4" ncloc="4" packages="1" files="1" classes="1"/>
    <file name="db.config.ts" path="E:\The-Salty-Devs\server\src\utils\db.config.ts">
      <metrics statements="4" coveredstatements="4" conditionals="2" coveredconditionals="1" methods="0" coveredmethods="0"/>
      <line num="1" count="1" type="stmt"/>
      <line num="9" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="10" count="1" type="stmt"/>
      <line num="18" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
