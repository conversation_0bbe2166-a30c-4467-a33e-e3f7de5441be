import { Router } from "express";
import * as Post<PERSON>ontroller from "./post.controller.ts";
import { isAuthUser } from "@middlewares/isAuthUser.middleware.ts";
import { isAdminAuth } from "@middlewares/isAdminAuth.middleware.ts";

const router = Router()

router.get("/", PostController.getPosts)
router.get("/:slug", isAuth<PERSON>ser, PostController.getPost)
router.post("/", isAdmin<PERSON>uth, PostController.createPost)
router.put("/update/:slug", isAdminAuth, PostController.updatePost)
router.delete("/:id", isAdminAuth, PostController.deletePost)

export default router;