import React from 'react';
import Link from 'next/link';
import { Eye, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Post } from '@/types';

interface PostCardProps {
  post: Post;
}

const PostCard = ({ post }: PostCardProps) => {
  return (
    <Link href={`/articles/${post.slug}`}>
      <article className="group bg-card border border-border p-6 hover:gradient-border transition-all duration-300 h-full flex flex-col">
        <div className="space-y-4 flex-1">
          {/* Category and View Count*/}
          <div className="flex items-center justify-between">
            {/* <Badge variant="secondary" className="text-xs">
              {post.category}
            </Badge> */}
            {post.views > 0 && (
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <Eye className="h-3 w-3" />
                <span>{post.views.toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-card-foreground group-hover:gradient-text transition-colors line-clamp-2">
            {post.title}
          </h2>

          {/* Excerpt */}
          <p className="text-muted-foreground leading-relaxed line-clamp-3 flex-1">
            {post.description}
          </p>
        </div>

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-border space-y-3">
          {/* Author and Date */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2 text-secondary-foreground">
              <User className="h-3 w-3" />
              <span>{post.author.name}</span>
            </div>
            <time className="text-secondary-foreground font-mono">
              {post.publishedAt}
            </time>
          </div>

          {/* Tags */}
          {post.tags?.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
      </article>
    </Link>
  );
};

export default PostCard;
