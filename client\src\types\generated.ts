// AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
// Generated from Prisma schema at: 2025-10-06T13:33:58.761Z
// To regenerate, run: npm run generate:types in the server directory

// ============================================================================
// ENUMS
// ============================================================================

export enum Role {
  ADMIN = 'ADMIN',
  USER = 'USER'
}

// ============================================================================
// BASE MODEL INTERFACES
// ============================================================================

export interface User {
  id: string;
  email: string;
  password: string;
  name: string | null;
  role: Role;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  bio: string | null;
}

export interface Post {
  id: string;
  title: string;
  description: string | null;
  content: string | null;
  slug: string | null;
  published: boolean;
  authorId: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  publishedAt: string | null; // ISO string
  imageURL: string | null;
  views: number;
  categoryId: string | null;
  tags: string[];
}

export interface Comment {
  id: string;
  content: string;
  createdAt: string; // ISO string
  editedAt: string; // ISO string
  authorId: string;
  postId: string;
}

export interface Category {
  id: string;
  name: string;
}

export interface PostViews {
  id: string;
  viewedAt: string; // ISO string
  visitorId: string;
  postId: string;
}

// ============================================================================
// EXTENDED INTERFACES WITH RELATIONS
// ============================================================================

export interface UserWithPosts extends User {
  posts: Post[];
}

export interface UserWithComments extends User {
  comments: Comment[];
}

export interface UserWithRelations extends User {
  posts: Post[];
  comments: Comment[];
  views: PostViews[];
}

export interface PostWithAuthor extends Post {
  author: User;
}

export interface PostWithCategory extends Post {
  category: Category | null;
}

export interface PostWithComments extends Post {
  comments: Comment[];
}

export interface PostWithAuthorAndCategory extends Post {
  author: User;
  category: Category | null;
}

export interface PostWithRelations extends Post {
  author: User;
  category: Category | null;
  comments: CommentWithAuthor[];
  viewedBy: PostViews[];
}

export interface CommentWithAuthor extends Comment {
  author: User;
}

export interface CommentWithPost extends Comment {
  post: Post;
}

export interface CommentWithRelations extends Comment {
  author: User;
  post: Post;
}

export interface CategoryWithPosts extends Category {
  posts: Post[];
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// CREATE/UPDATE INPUT TYPES
// ============================================================================

export interface CreateUserInput {
  email: string;
  password: string;
  name?: string;
  role?: Role;
  bio?: string;
}

export interface UpdateUserInput {
  email?: string;
  name?: string;
  role?: Role;
  bio?: string;
}

export interface CreatePostInput {
  title: string;
  description?: string;
  content?: string;
  slug?: string;
  published?: boolean;
  authorId: string;
  publishedAt?: string;
  imageURL?: string;
  categoryId?: string;
  tags?: string[];
}

export interface UpdatePostInput {
  title?: string;
  description?: string;
  content?: string;
  slug?: string;
  published?: boolean;
  publishedAt?: string;
  imageURL?: string;
  categoryId?: string;
  tags?: string[];
}

export interface CreateCommentInput {
  content: string;
  authorId: string;
  postId: string;
}

export interface UpdateCommentInput {
  content?: string;
}

export interface CreateCategoryInput {
  name: string;
}

export interface UpdateCategoryInput {
  name?: string;
}

// ============================================================================
// QUERY FILTER TYPES
// ============================================================================

export interface PostFilters {
  published?: boolean;
  authorId?: string;
  categoryId?: string;
  tags?: string[];
  search?: string;
}

export interface UserFilters {
  role?: Role;
  search?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type PostStatus = 'draft' | 'published';
export type SortOrder = 'asc' | 'desc';

export interface SortOptions {
  field: string;
  order: SortOrder;
}

// Legacy compatibility - keeping old interface names for backward compatibility
// TODO: Gradually migrate to new naming convention
export interface Author extends User {}
