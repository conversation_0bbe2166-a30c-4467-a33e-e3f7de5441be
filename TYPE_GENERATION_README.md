# Automatic Type Generation from Prisma Schema

This project includes an automated system to generate TypeScript types for the frontend based on the backend Prisma schema. This ensures type safety and consistency between your backend models and frontend interfaces.

## 🎯 Purpose

- **Type Safety**: Automatically sync frontend types with backend Prisma models
- **Consistency**: Eliminate manual type definitions that can become outdated
- **Developer Experience**: Get accurate IntelliSense and type checking in your frontend
- **Maintainability**: Single source of truth for data models

## 📁 File Structure

```
server/
├── scripts/
│   └── generate-frontend-types.ts    # Type generation script
├── prisma/
│   └── schema.prisma                 # Source of truth for data models
└── package.json                      # Contains type generation scripts

client/
└── src/
    └── types/
        ├── generated.ts              # AUTO-GENERATED - Do not edit manually
        └── index.ts                  # Re-exports generated types + legacy compatibility
```

## 🚀 Usage

### Automatic Generation (Recommended)

The types are automatically generated when you run any of these commands:

```bash
# From root directory - installs dependencies and generates types
npm run iall

# From root directory - just generate types
npm run generate:types

# From server directory - generate Prisma client and frontend types
npm run prisma:generate:full
```

### Manual Generation

If you need to regenerate types manually:

```bash
# From server directory
npm run generate:types

# Or run the script directly
npx tsx scripts/generate-frontend-types.ts
```

## 📋 Generated Types

The script generates comprehensive TypeScript interfaces including:

### Base Model Interfaces
- `User` - User model with all fields
- `Post` - Post model with all fields  
- `Comment` - Comment model with all fields
- `Category` - Category model with all fields
- `PostViews` - PostViews model with all fields

### Enums
- `Role` - User roles (ADMIN, USER)

### Extended Interfaces with Relations
- `UserWithPosts` - User with posts array
- `PostWithAuthor` - Post with author details
- `PostWithCategory` - Post with category details
- `PostWithComments` - Post with comments array
- `PostWithRelations` - Post with all relations
- `CommentWithAuthor` - Comment with author details
- And many more...

### API Response Types
- `ApiResponse<T>` - Standard API response wrapper
- `PaginatedResponse<T>` - Paginated response structure

### Input Types
- `CreateUserInput`, `UpdateUserInput`
- `CreatePostInput`, `UpdatePostInput`
- `CreateCommentInput`, `UpdateCommentInput`
- `CreateCategoryInput`, `UpdateCategoryInput`

### Filter Types
- `PostFilters` - For filtering posts
- `UserFilters` - For filtering users

## 💡 How to Use in Frontend

```typescript
// Import specific types you need
import { User, Post, PostWithAuthor, CreatePostInput } from '@/types/generated';

// Or import from the main types file (includes legacy compatibility)
import { User, Post } from '@/types';

// Example usage
const user: User = {
  id: '123',
  email: '<EMAIL>',
  name: 'John Doe',
  role: Role.USER,
  // ... other fields
};

const postWithAuthor: PostWithAuthor = {
  id: '456',
  title: 'My Post',
  author: user,
  // ... other fields
};
```

## 🔄 Workflow Integration

The type generation is integrated into your development workflow:

1. **Schema Changes**: When you modify `server/prisma/schema.prisma`
2. **Run Generation**: Execute `npm run iall` or `npm run prisma:generate:full`
3. **Types Updated**: Frontend types are automatically updated
4. **Type Safety**: Your frontend code gets updated type checking

## ⚠️ Important Notes

### DO NOT Edit Generated Files
- Never manually edit `client/src/types/generated.ts`
- The file is completely regenerated each time the script runs
- Add custom types to separate files or extend the generation script

### Legacy Compatibility
- The `client/src/types/index.ts` file provides backward compatibility
- Old interface names (`Author`, `Post`, `Comment`) are marked as deprecated
- Gradually migrate to the new generated types

### Date Handling
- All date fields are typed as `string` (ISO format)
- Convert to `Date` objects in your frontend code when needed

## 🛠️ Customization

To customize the generated types, modify `server/scripts/generate-frontend-types.ts`:

1. Add new interface combinations
2. Modify field types (e.g., Date vs string)
3. Add utility types specific to your frontend needs
4. Include additional API response structures

## 🔧 Troubleshooting

### Script Not Running
```bash
# Make sure tsx is available
npm install tsx --save-dev

# Run with full path
npx tsx server/scripts/generate-frontend-types.ts
```

### Types Not Updating
1. Check if the script ran successfully
2. Verify the timestamp in the generated file header
3. Restart your TypeScript language server in your IDE

### Import Errors
```typescript
// Use absolute imports with your configured path mapping
import { User } from '@/types/generated';

// Or relative imports
import { User } from '../types/generated';
```

## 📈 Benefits

1. **Reduced Bugs**: Catch type mismatches at compile time
2. **Better DX**: Accurate autocomplete and IntelliSense
3. **Maintainability**: Single source of truth for data structures
4. **Consistency**: Frontend and backend always in sync
5. **Productivity**: No manual type maintenance required

## 🔮 Future Enhancements

- Add validation schema generation (Zod schemas)
- Generate API client types
- Add GraphQL type generation
- Create form validation schemas
- Generate mock data factories
